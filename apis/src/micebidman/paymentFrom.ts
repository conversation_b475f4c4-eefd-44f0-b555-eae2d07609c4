import { download, get, post } from '../request'
import {
    IPaymentFromFilter,
    IPaymentFrom,
    ICreatePaymentFromParams,
    IConfirmPaymentParams,
    IRejectPaymentParams,
    IUploadInvoiceParams,
    IPageResponse,
    Result
} from '@haierbusiness-front/common-libs'


export const paymentFromApi = {
    // 收款单详情查询-缴费
    getPaymentDetailss: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getDetails', {
            id
        })
    },
    // 财务确认付款并上传付款凭证-缴费
    confirmPaymentPayments: (params: IConfirmPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/confirm/receivePayment', params)
    },
    // 财务驳回付款-缴费
    rejectPayments: (params: IRejectPaymentParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/reject/receivePayment', params)
    },
    // 上传发票-缴费
    uploadInvoice: (params: IUploadInvoiceParams): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/upload/invoice', params)
    },
    // 删除收款单-缴费
    removePayment: (id: number): Promise<Result> => {
        return post('/mice-bid/api/mice/plat/receive/payment/delete', { id })
    },


    getPayMentList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/plat/receive/payment/getPage', params)
    },


    // 收款单列表查询
    getMerchantPayMentList: (params: IPaymentFromFilter): Promise<IPageResponse<IPaymentFrom>> => {
        return get('/mice-bid/api/mice/merchant/payment/getPage', params)
    },
    // 查询付款单详情
    getMerchantDetails: (id: number): Promise<IPaymentFrom> => {
        return get('/mice-bid/api/mice/merchant/payment/getDetails', {
            id
        })
    },

    // 供应商上传发票
    uploadPaymentInvoice: (params: IUploadInvoiceParams): Promise<Result> => {
        return post('/mice-bid/api/mice/merchant/payment/upload/invoice', params)
    },

}
