<script setup lang="ts">
import { ref, inject, onMounted, reactive } from 'vue';
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue';
import { meetingProcessOrchestration, resolveParam } from '@haierbusiness-front/utils';
import { Button, Dropdown, Menu, Pagination, BadgeRibbon, Tooltip, Popover, message, Modal } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { miceBidManOrderListApi, schemeApi } from '@haierbusiness-front/apis';
import meetingDetail from '@haierbusiness-front/components/mice/orderList/meetingDetail.vue';
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue';
const route = useRoute();
const frameModel = ref(inject<any>('frameModel'));
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
});
const viewSelect = ref('demand');
const presentConfirm = async () => {
  Modal.confirm({
    title: '账单确认',
    content: '是否确认全部账单？',
    async onOk() {
      const res = await miceBidManOrderListApi.userConfirm({
        miceId: routeQuery.record.miceId,
        confirmState: 1,
      });
      console.log(res);
      if (res.success) {
        message.success('确认成功！');
        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
        // 跳转需求确认页面
        const url =
          (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
          '/card-order/miceOrder';
        window.location.href = url;
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
};
const open = ref(false);
const reason = ref<string>(''); // 驳回原因

const rejectList = ref();
const rejectColumns = [
  { title: '驳回', dataIndex: 'checked', key: 'checked', width: 100 },
  { title: '服务商', dataIndex: 'provider', key: 'provider', width: 200 },
  { title: '驳回原因', dataIndex: 'reason', key: 'reason' },
];
const getRejectList = async () => {
  rejectList.value = billUploadSchemeRef.value.tempDataList;
  open.value = true;
};
const cancelConfirm = async () => {
  const selected = rejectList.value.filter((item) => item.checked);
  if (selected.length === 0) {
    message.error('请至少选择一个需要驳回的服务商！');
    return;
  }
  if (selected.some((item) => !item.reason)) {
    message.error('请填写所有已选服务商的驳回原因！');
    return;
  }
  const rejectBills = selected.map((item) => ({
    rejectBillId: item.id,
    reason: item.reason,
  }));
  const res = await miceBidManOrderListApi.userConfirm({
    miceId: routeQuery.record.miceId,
    confirmState: 2,
    rejectBills: rejectBills,
  });
  console.log(res);
  if (res.success) {
    message.success('确认成功！');
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
    // 跳转需求确认页面
    const url =
      (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder';
    window.location.href = url;
  }
  // TODO: 调用接口，传递 selected
  message.success('驳回成功！');

  open.value = false;
};
const switchBidLoading = ref(false);
const billUploadSchemeRef = ref();

const priceChangeColumnsScheme = ref([
  {
    title: '切换前服务商',
    dataIndex: 'merchantNameOriginal',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '切换后服务商',
    dataIndex: 'merchantNameNew',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '变更说明',
    dataIndex: 'reason',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '附件',
    dataIndex: 'doc',
    align: 'center',
    ellipsis: true,
  },
]);
const priceChangeListScheme = ref([]);
const showSwitchBidSchemeModal = async () => {
  console.log(routeQuery);
  let res = await schemeApi.switchRecord({
    miceId: routeQuery.record.miceId,
  });
  priceChangeListScheme.value = res.slice(0, 1).map((item) => {
    if (item.originalMerchants)
      item.merchantNameOriginal =
        `<div>` + item.originalMerchants.map((item1) => item1.merchantName).join('</div>') + `</div>`;
    else item.merchantNameOriginal = '-';
    if (item.newMerchants)
      item.merchantNameNew = `<div>` + item.newMerchants.map((item1) => item1.merchantName).join('</div>') + `</div>`;
    else item.merchantNameNew = '-';
    let str = '';
    let document = {};
    item.schemeBidSwitchPaths.forEach((item1, index) => {
      try {
        document = JSON.parse(item1);
      } catch (error) {
        console.log(error);
      }

      str += `<a target='_blank' href='${document.url}'>${
        document.name
      }</a><span style='margin-right: 10px;color: #86909c' >${
        index == item.schemeBidSwitchPaths?.length - 1 ? '' : ','
      }</span>`;
    });
    item.doc = str;
    return item;
  });
};
onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0;
  showSwitchBidSchemeModal();
});
</script>

<template>
  <div class="container">
    <div class="changeScheme" style="margin-bottom: 16px">
      <a-table
        :columns="priceChangeColumnsScheme"
        :data-source="priceChangeListScheme"
        :row-key="(record: { id: string; }) => record.id"
        :pagination="false"
        bordered
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <span v-html="record[column.dataIndex]"></span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  background: #f1f2f6;
  padding: 0 auto;
  height: calc(100vh - 60px);
}
.changeScheme {
  background: #fff;
  margin: 0 auto;
  width: 1280px !important;
  left: calc(50% - 640px);
  height: 100%;
}
.footer {
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  right: 0;
  background: #fff;
  left: calc(50% - 640px);
  z-index: 11;
  width: 1280px !important;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}
:deep(.ant-table-thead) {
  th {
    color: #86909c !important;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}
:deep(.ant-table-cell) {
  padding: 12px 8px !important;
  line-height: 20px;
}
</style>
